#!/usr/bin/env python3
"""
TalentSol ATS - Synthetic Data Generator for Dashboard Metrics
Generates synthetic data to fix "Time to Hire" and "Interviews This Week" metrics
"""

import os
import sys
import asyncio
import asyncpg
from datetime import datetime, timedelta
from typing import List, Dict, Any
import random
import uuid

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'user': 'talentsol_user',
    'password': 'talentsol_password',
    'database': 'talentsol_ats'
}

class SyntheticDataGenerator:
    def __init__(self):
        self.connection = None
        
    async def connect(self):
        """Connect to PostgreSQL database"""
        try:
            self.connection = await asyncpg.connect(**DB_CONFIG)
            print("✅ Connected to TalentSol database")
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            sys.exit(1)
    
    async def disconnect(self):
        """Disconnect from database"""
        if self.connection:
            await self.connection.close()
            print("🔌 Disconnected from database")
    
    async def get_existing_data(self) -> Dict[str, List[Any]]:
        """Get existing applications and users for reference"""
        print("🔍 Fetching existing data...")
        
        # Get existing applications
        applications = await self.connection.fetch("""
            SELECT id, candidate_id, job_id, status, submitted_at 
            FROM applications 
            ORDER BY submitted_at DESC 
            LIMIT 20
        """)
        
        # Get existing users (for created_by_id)
        users = await self.connection.fetch("""
            SELECT id FROM users LIMIT 5
        """)
        
        print(f"📊 Found {len(applications)} applications and {len(users)} users")
        
        return {
            'applications': applications,
            'users': users
        }
    
    async def create_hired_applications(self, applications: List[Any]) -> int:
        """Update some applications to 'hired' status with hiredAt dates"""
        print("🎯 Creating hired applications for Time to Hire metric...")
        
        if not applications:
            print("⚠️  No applications found to update")
            return 0
        
        # Select first 5 applications to mark as hired
        hired_count = 0
        for i, app in enumerate(applications[:5]):
            # Calculate hired date (15-45 days after submission)
            submitted_at = app['submitted_at']
            days_to_hire = random.randint(15, 45)
            hired_at = submitted_at + timedelta(days=days_to_hire)
            
            # Update application status and hired_at
            await self.connection.execute("""
                UPDATE applications 
                SET status = 'hired', hired_at = $1
                WHERE id = $2
            """, hired_at, app['id'])
            
            hired_count += 1
            print(f"  ✅ Updated application {app['id']} - hired after {days_to_hire} days")
        
        return hired_count
    
    async def create_upcoming_interviews(self, applications: List[Any], users: List[Any]) -> int:
        """Create upcoming interviews for the next 7 days"""
        print("📅 Creating upcoming interviews for Interviews This Week metric...")
        
        if not applications or not users:
            print("⚠️  Insufficient data to create interviews")
            return 0
        
        # Interview types and locations
        interview_types = ['technical', 'behavioral', 'panel', 'cultural_fit', 'final']
        locations = ['Video Call - Zoom', 'Office Conference Room A', 'Office Conference Room B', 'Video Call - Teams']
        
        created_count = 0
        now = datetime.now()
        
        # Create 8 interviews over the next 7 days
        for i in range(8):
            app = applications[i % len(applications)]
            user = users[i % len(users)]
            
            # Generate interview date within next 7 days
            days_ahead = random.randint(0, 6)
            hours_ahead = random.randint(9, 17)  # 9 AM to 5 PM
            minutes = random.choice([0, 30])  # On the hour or half hour
            
            scheduled_date = now + timedelta(days=days_ahead, hours=hours_ahead-now.hour, minutes=minutes-now.minute, seconds=-now.second, microseconds=-now.microsecond)
            
            # Generate interview data
            interview_id = f"int_{uuid.uuid4().hex[:8]}"
            interview_type = random.choice(interview_types)
            location = random.choice(locations)
            duration = random.choice([45, 60, 90])  # minutes
            
            # Insert interview
            await self.connection.execute("""
                INSERT INTO interviews (
                    id, application_id, type, scheduled_date, duration, 
                    location, status, notes, created_by_id, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            """, 
                interview_id,
                app['id'],
                interview_type,
                scheduled_date,
                duration,
                location,
                'scheduled',
                f"{interview_type.title()} interview with candidate",
                user['id'],
                now,
                now
            )
            
            created_count += 1
            print(f"  ✅ Created {interview_type} interview on {scheduled_date.strftime('%Y-%m-%d %H:%M')}")
        
        return created_count
    
    async def update_application_statuses(self, applications: List[Any]) -> int:
        """Update some applications to 'interview' status for consistency"""
        print("🔄 Updating application statuses...")
        
        updated_count = 0
        # Update applications 6-10 to 'interview' status
        for app in applications[5:10]:
            await self.connection.execute("""
                UPDATE applications 
                SET status = 'interview'
                WHERE id = $1
            """, app['id'])
            
            updated_count += 1
            print(f"  ✅ Updated application {app['id']} to 'interview' status")
        
        return updated_count
    
    async def verify_data(self):
        """Verify the created data"""
        print("🔍 Verifying created data...")
        
        # Check hired applications
        hired_count = await self.connection.fetchval("""
            SELECT COUNT(*) FROM applications 
            WHERE status = 'hired' AND hired_at IS NOT NULL
        """)
        
        # Check upcoming interviews
        now = datetime.now()
        next_week = now + timedelta(days=7)
        upcoming_interviews = await self.connection.fetchval("""
            SELECT COUNT(*) FROM interviews 
            WHERE scheduled_date BETWEEN $1 AND $2 AND status = 'scheduled'
        """, now, next_week)
        
        print(f"📊 Verification Results:")
        print(f"  - Hired applications: {hired_count}")
        print(f"  - Upcoming interviews (next 7 days): {upcoming_interviews}")
        
        return hired_count > 0 and upcoming_interviews > 0

async def main():
    """Main execution function"""
    print("🚀 TalentSol ATS - Synthetic Data Generator")
    print("=" * 50)
    
    generator = SyntheticDataGenerator()
    
    try:
        # Connect to database
        await generator.connect()
        
        # Get existing data
        existing_data = await generator.get_existing_data()
        
        # Create synthetic data
        hired_count = await generator.create_hired_applications(existing_data['applications'])
        interview_count = await generator.create_upcoming_interviews(
            existing_data['applications'], 
            existing_data['users']
        )
        status_count = await generator.update_application_statuses(existing_data['applications'])
        
        # Verify data
        verification_passed = await generator.verify_data()
        
        print("\n" + "=" * 50)
        print("📈 Summary:")
        print(f"  - Created {hired_count} hired applications")
        print(f"  - Created {interview_count} upcoming interviews")
        print(f"  - Updated {status_count} application statuses")
        print(f"  - Verification: {'✅ PASSED' if verification_passed else '❌ FAILED'}")
        
        if verification_passed:
            print("\n🎉 Synthetic data generation completed successfully!")
            print("💡 You can now check the Dashboard - both metrics should show data")
        else:
            print("\n⚠️  Data verification failed - please check the database")
        
    except Exception as e:
        print(f"❌ Error during data generation: {e}")
        sys.exit(1)
    
    finally:
        await generator.disconnect()

if __name__ == "__main__":
    # Check if asyncpg is installed
    try:
        import asyncpg
    except ImportError:
        print("❌ asyncpg library not found")
        print("💡 Install it with: pip install asyncpg")
        sys.exit(1)
    
    # Run the generator
    asyncio.run(main())
